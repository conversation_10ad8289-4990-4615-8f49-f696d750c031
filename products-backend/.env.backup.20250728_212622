# 备份服务器配置 (152.89.168.61)
# 用于连接到备份服务器的环境变量配置

NODE_ENV=development
PORT=3000

# MongoDB 配置 - 备份服务器
MONGODB_URI=************************************************************************

# MinIO 配置 - 备份服务器
MINIO_ENDPOINT=152.89.168.61
MINIO_PORT=9000
MINIO_ACCESS_KEY=lcsm
MINIO_SECRET_KEY=Sa2482047260@
MINIO_BUCKET=product-images

# Redis 配置 - 备份服务器
REDIS_URL=redis://152.89.168.61:6379

# 日志配置
LOG_LEVEL=info

# 飞书API配置 (与主服务器相同)
FEISHU_APP_ID=cli_a8e575c35763d013
FEISHU_APP_SECRET=41VyUJHWqFBoiOr5dOwgqctKwSn1RqWf
FEISHU_APP_TOKEN=JLAKwFpTmiYgRGkVY7kcOGZenNg
FEISHU_TABLE_ID=tbl2V3mVBZHOLjrx
FEISHU_BASE_URL=https://open.feishu.cn/open-apis/
FEISHU_REQUEST_TIMEOUT=10000
FEISHU_RETRY_ATTEMPTS=3
FEISHU_RETRY_DELAY=1000

# 数据同步配置
SYNC_SCHEDULER_ENABLED=true
TIMEZONE=Asia/Shanghai

# 同步性能配置
SYNC_BATCH_SIZE=50
SYNC_CONCURRENT_IMAGES=5
SYNC_RETRY_ATTEMPTS=3
SYNC_TIMEOUT=300000
SYNC_MAX_RECORDS=10000

# 图片处理配置
IMAGE_MAX_SIZE=10485760
IMAGE_ALLOWED_TYPES=jpg,jpeg,png,webp,gif
IMAGE_QUALITY=85
IMAGE_RESIZE_WIDTH=1200
IMAGE_RESIZE_HEIGHT=800

# WebSocket配置
WS_ENABLED=true
WS_PORT=3001
WS_HEARTBEAT_INTERVAL=30000

# 数据验证配置
DATA_VALIDATION_ENABLED=true
DATA_REPAIR_ENABLED=true
